using Microsoft.Extensions.Logging;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Exceptions;
using UNI.Utilities.ExternalStorage.Models;
using UNI.Utilities.MinIo;
using UNI.Utilities.MinIo.Models;

namespace UNI.Utilities.ExternalStorage.Providers
{
    /// <summary>
    /// MinIO storage provider implementation
    /// </summary>
    public class MinIOStorageProvider : BaseStorageProvider<MinIOStorageSettings>
    {
        private readonly IMinIoService _minIoService;

        /// <summary>
        /// Constructor for MinIOStorageProvider
        /// </summary>
        /// <param name="settings">MinIO storage settings</param>
        /// <param name="logger">Logger instance</param>
        public MinIOStorageProvider(MinIOStorageSettings settings, ILogger<MinIOStorageProvider> logger)
            : base(settings, logger)
        {
            // Create MinIO service with converted settings
            var minIoSettings = ConvertToMinIoSettings(settings);
            _minIoService = new MinIoService(minIoSettings, logger);
        }

        /// <summary>
        /// Storage provider type
        /// </summary>
        public override StorageProviderType ProviderType => StorageProviderType.MinIO;

        /// <summary>
        /// Storage provider name
        /// </summary>
        public override string ProviderName => "MinIO";

        /// <summary>
        /// Validate MinIO-specific settings
        /// </summary>
        protected override void ValidateSettings()
        {
            base.ValidateSettings();

            if (string.IsNullOrWhiteSpace(_settings.Endpoint))
                throw new StorageConfigurationException("MinIO Endpoint is required", ProviderType);

            if (string.IsNullOrWhiteSpace(_settings.AccessKey))
                throw new StorageConfigurationException("MinIO AccessKey is required", ProviderType);

            if (string.IsNullOrWhiteSpace(_settings.SecretKey))
                throw new StorageConfigurationException("MinIO SecretKey is required", ProviderType);
        }

        /// <summary>
        /// Convert external storage settings to MinIO settings
        /// </summary>
        /// <param name="settings">External storage settings</param>
        /// <returns>MinIO settings</returns>
        private MinIoSettings ConvertToMinIoSettings(MinIOStorageSettings settings)
        {
            return new MinIoSettings
            {
                Endpoint = settings.Endpoint,
                ProxyEndpoint = settings.ProxyEndpoint,
                AccessKey = settings.AccessKey,
                SecretKey = settings.SecretKey,
                UseSSL = settings.UseSSL,
                Region = settings.Region,
                DefaultBucket = settings.DefaultBucket,
                TimeoutSeconds = settings.TimeoutSeconds,
                CreateBucketIfNotExists = settings.CreateBucketIfNotExists
            };
        }

        #region Bucket Operations

        public override async Task<bool> BucketExistsAsync(string bucketName, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _minIoService.BucketExistsAsync(bucketName, cancellationToken);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to check if bucket '{bucketName}' exists", ProviderType, innerException: ex);
            }
        }

        public override async Task CreateBucketAsync(string bucketName, string? region = null, CancellationToken cancellationToken = default)
        {
            try
            {
                await _minIoService.CreateBucketAsync(bucketName, region, cancellationToken);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to create bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DeleteBucketAsync(string bucketName, CancellationToken cancellationToken = default)
        {
            try
            {
                await _minIoService.DeleteBucketAsync(bucketName, cancellationToken);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to delete bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<BucketInfo>> ListBucketsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var buckets = await _minIoService.ListBucketsAsync(cancellationToken);
                return buckets.Select(ConvertToBucketInfo);
            }
            catch (Exception ex)
            {
                throw new StorageException("Failed to list buckets", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region Object Operations

        public override async Task<StorageUploadResult> UploadObjectAsync(
            string bucketName, 
            string objectName, 
            Stream stream, 
            long? size = null, 
            string? contentType = null, 
            Dictionary<string, string>? metadata = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                var result = await _minIoService.UploadObjectAsync(bucketName, objectName, stream, size, contentType, metadata, cancellationToken);
                return ConvertToStorageUploadResult(result);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to upload object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<StorageUploadResult> UploadFileAsync(
            string bucketName, 
            string objectName, 
            string filePath, 
            string? contentType = null, 
            Dictionary<string, string>? metadata = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                var result = await _minIoService.UploadFileAsync(bucketName, objectName, filePath, contentType, metadata, cancellationToken);
                return ConvertToStorageUploadResult(result);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to upload file '{filePath}' as object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<StorageUploadResult> UploadLargeFileAsync(
            string bucketName, 
            string objectName, 
            string filePath, 
            string? contentType = null, 
            Dictionary<string, string>? metadata = null, 
            long partSize = 67108864, 
            IProgress<StorageUploadProgress>? progressCallback = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Convert progress callback
                IProgress<UploadProgress>? minIoProgressCallback = null;
                if (progressCallback != null)
                {
                    minIoProgressCallback = new Progress<UploadProgress>(progress =>
                    {
                        var storageProgress = ConvertToStorageUploadProgress(progress);
                        progressCallback.Report(storageProgress);
                    });
                }

                var result = await _minIoService.UploadLargeFileAsync(bucketName, objectName, filePath, contentType, metadata, partSize, minIoProgressCallback, cancellationToken);
                return ConvertToStorageUploadResult(result);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to upload large file '{filePath}' as object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DownloadObjectAsync(string bucketName, string objectName, Stream stream, CancellationToken cancellationToken = default)
        {
            try
            {
                await _minIoService.DownloadObjectAsync(bucketName, objectName, stream, cancellationToken);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to download object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DownloadFileAsync(string bucketName, string objectName, string filePath, CancellationToken cancellationToken = default)
        {
            try
            {
                await _minIoService.DownloadFileAsync(bucketName, objectName, filePath, cancellationToken);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to download object '{objectName}' from bucket '{bucketName}' to file '{filePath}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<byte[]> GetObjectBytesAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _minIoService.GetObjectBytesAsync(bucketName, objectName, cancellationToken);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to get object bytes for '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<bool> ObjectExistsAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _minIoService.ObjectExistsAsync(bucketName, objectName, cancellationToken);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to check if object '{objectName}' exists in bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DeleteObjectAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                await _minIoService.DeleteObjectAsync(bucketName, objectName, cancellationToken);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to delete object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<StorageDeleteResult>> DeleteObjectsAsync(string bucketName, IEnumerable<string> objectNames, CancellationToken cancellationToken = default)
        {
            try
            {
                var results = await _minIoService.DeleteObjectsAsync(bucketName, objectNames, cancellationToken);
                return results.Select(ConvertToStorageDeleteResult);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to delete objects from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<StorageObjectInfo> GetObjectInfoAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                var objectInfo = await _minIoService.GetObjectInfoAsync(bucketName, objectName, cancellationToken);
                return ConvertToStorageObjectInfo(objectInfo);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to get object info for '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<StorageObjectInfo>> ListObjectsAsync(string bucketName, string? prefix = null, bool recursive = true, CancellationToken cancellationToken = default)
        {
            try
            {
                var objects = await _minIoService.ListObjectsAsync(bucketName, prefix, recursive, cancellationToken);
                return objects.Select(ConvertToStorageObjectInfo);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to list objects in bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region URL Operations

        public override async Task<string> GetPresignedDownloadUrlAsync(string bucketName, string objectName, TimeSpan expiryTimeSpan, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _minIoService.GetPresignedDownloadUrlAsync(bucketName, objectName, expiryTimeSpan, cancellationToken);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to get presigned download URL for object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<string> GetPresignedUploadUrlAsync(string bucketName, string objectName, TimeSpan expiryTimeSpan, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _minIoService.GetPresignedUploadUrlAsync(bucketName, objectName, expiryTimeSpan, cancellationToken);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to get presigned upload URL for object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<string> GetPreviewUrlAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _minIoService.GetPreviewUrlAsync(bucketName, objectName, cancellationToken);
            }
            catch (Exception ex)
            {
                throw new StorageException($"Failed to get preview URL for object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region Conversion Methods

        private BucketInfo ConvertToBucketInfo(UNI.Utilities.MinIo.Models.BucketInfo minIoBucket)
        {
            return new BucketInfo
            {
                Name = minIoBucket.Name,
                CreationDate = minIoBucket.CreationDate,
                Region = minIoBucket.Region,
                Metadata = minIoBucket.Metadata ?? new Dictionary<string, string>()
            };
        }

        private StorageUploadResult ConvertToStorageUploadResult(UploadResult minIoResult)
        {
            return new StorageUploadResult
            {
                BucketName = minIoResult.BucketName,
                ObjectName = minIoResult.ObjectName,
                Size = minIoResult.Size,
                ETag = minIoResult.ETag,
                ContentType = minIoResult.ContentType,
                UploadedAt = minIoResult.UploadedAt,
                Metadata = minIoResult.Metadata ?? new Dictionary<string, string>(),
                ProviderData = new Dictionary<string, object>
                {
                    ["MinIOVersionId"] = minIoResult.VersionId ?? string.Empty,
                    ["MinIOLocation"] = minIoResult.Location ?? string.Empty
                }
            };
        }

        private StorageUploadProgress ConvertToStorageUploadProgress(UploadProgress minIoProgress)
        {
            return new StorageUploadProgress
            {
                TotalBytes = minIoProgress.TotalBytes,
                UploadedBytes = minIoProgress.UploadedBytes,
                CurrentPart = minIoProgress.CurrentPart,
                TotalParts = minIoProgress.TotalParts,
                BytesPerSecond = minIoProgress.BytesPerSecond,
                EstimatedTimeRemaining = minIoProgress.EstimatedTimeRemaining
            };
        }

        private StorageDeleteResult ConvertToStorageDeleteResult(DeleteResult minIoResult)
        {
            return new StorageDeleteResult
            {
                ObjectName = minIoResult.ObjectName,
                Success = minIoResult.Success,
                ErrorMessage = minIoResult.ErrorMessage,
                ErrorCode = minIoResult.ErrorCode
            };
        }

        private StorageObjectInfo ConvertToStorageObjectInfo(ObjectInfo minIoObjectInfo)
        {
            return new StorageObjectInfo
            {
                ObjectName = minIoObjectInfo.ObjectName,
                Size = minIoObjectInfo.Size,
                LastModified = minIoObjectInfo.LastModified,
                ETag = minIoObjectInfo.ETag,
                ContentType = minIoObjectInfo.ContentType,
                StorageClass = minIoObjectInfo.StorageClass,
                IsDirectory = minIoObjectInfo.IsDirectory,
                Metadata = minIoObjectInfo.Metadata ?? new Dictionary<string, string>(),
                ProviderData = new Dictionary<string, object>
                {
                    ["MinIOVersionId"] = minIoObjectInfo.VersionId ?? string.Empty,
                    ["MinIOOwner"] = minIoObjectInfo.Owner ?? string.Empty
                }
            };
        }

        #endregion

        #region Dispose

        protected override void DisposeCore()
        {
            _minIoService?.Dispose();
        }

        #endregion
    }
}
