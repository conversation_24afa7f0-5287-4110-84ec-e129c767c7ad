using FirebaseAdmin;
using FirebaseAdmin.Auth;
using Google.Apis.Auth.OAuth2;
using Google.Cloud.Storage.V1;
using Microsoft.Extensions.Logging;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Exceptions;
using UNI.Utilities.ExternalStorage.Models;
using Object = Google.Cloud.Storage.V1.Object;

namespace UNI.Utilities.ExternalStorage.Providers
{
    /// <summary>
    /// Firebase storage provider implementation
    /// </summary>
    public class FirebaseStorageProvider : BaseStorageProvider<FirebaseStorageSettings>
    {
        private readonly StorageClient _storageClient;
        private readonly FirebaseApp _firebaseApp;
        private readonly string _defaultBucketName;

        /// <summary>
        /// Constructor for FirebaseStorageProvider
        /// </summary>
        /// <param name="settings">Firebase storage settings</param>
        /// <param name="logger">Logger instance</param>
        public FirebaseStorageProvider(FirebaseStorageSettings settings, ILogger<FirebaseStorageProvider> logger)
            : base(settings, logger)
        {
            _firebaseApp = InitializeFirebaseApp();
            _storageClient = CreateStorageClient();
            _defaultBucketName = GetDefaultBucketName();
        }

        /// <summary>
        /// Storage provider type
        /// </summary>
        public override StorageProviderType ProviderType => StorageProviderType.Firebase;

        /// <summary>
        /// Storage provider name
        /// </summary>
        public override string ProviderName => "Firebase Storage";

        /// <summary>
        /// Validate Firebase-specific settings
        /// </summary>
        protected override void ValidateSettings()
        {
            base.ValidateSettings();

            if (string.IsNullOrWhiteSpace(_settings.ProjectId))
                throw new StorageConfigurationException("Firebase ProjectId is required", ProviderType);

            if (string.IsNullOrWhiteSpace(_settings.ServiceAccountKeyPath) && string.IsNullOrWhiteSpace(_settings.ServiceAccountKeyJson))
                throw new StorageConfigurationException("Firebase ServiceAccountKeyPath or ServiceAccountKeyJson is required", ProviderType);
        }

        /// <summary>
        /// Initialize Firebase app
        /// </summary>
        /// <returns>Firebase app instance</returns>
        private FirebaseApp InitializeFirebaseApp()
        {
            try
            {
                GoogleCredential credential;

                if (!string.IsNullOrWhiteSpace(_settings.ServiceAccountKeyJson))
                {
                    credential = GoogleCredential.FromJson(_settings.ServiceAccountKeyJson);
                }
                else if (!string.IsNullOrWhiteSpace(_settings.ServiceAccountKeyPath))
                {
                    credential = GoogleCredential.FromFile(_settings.ServiceAccountKeyPath);
                }
                else
                {
                    throw new StorageConfigurationException("Firebase credentials not configured", ProviderType);
                }

                var options = new AppOptions
                {
                    Credential = credential,
                    ProjectId = _settings.ProjectId
                };

                var appName = _settings.AppName ?? $"ExternalStorage_{Guid.NewGuid():N}";
                
                // Check if app already exists
                try
                {
                    return FirebaseApp.GetInstance(appName);
                }
                catch (ArgumentException)
                {
                    // App doesn't exist, create it
                    return FirebaseApp.Create(options, appName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Firebase app");
                throw new StorageConfigurationException("Failed to initialize Firebase app", ProviderType, ex);
            }
        }

        /// <summary>
        /// Create Google Cloud Storage client
        /// </summary>
        /// <returns>Storage client</returns>
        private StorageClient CreateStorageClient()
        {
            try
            {
                GoogleCredential credential;

                if (!string.IsNullOrWhiteSpace(_settings.ServiceAccountKeyJson))
                {
                    credential = GoogleCredential.FromJson(_settings.ServiceAccountKeyJson);
                }
                else if (!string.IsNullOrWhiteSpace(_settings.ServiceAccountKeyPath))
                {
                    credential = GoogleCredential.FromFile(_settings.ServiceAccountKeyPath);
                }
                else
                {
                    throw new StorageConfigurationException("Firebase credentials not configured", ProviderType);
                }

                return StorageClient.Create(credential);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create Firebase storage client");
                throw new StorageConfigurationException("Failed to create Firebase storage client", ProviderType, ex);
            }
        }

        /// <summary>
        /// Get default bucket name
        /// </summary>
        /// <returns>Default bucket name</returns>
        private string GetDefaultBucketName()
        {
            if (!string.IsNullOrWhiteSpace(_settings.StorageBucket))
                return _settings.StorageBucket;

            if (!string.IsNullOrWhiteSpace(_settings.DefaultBucket))
                return _settings.DefaultBucket;

            // Use project ID as default bucket name
            return $"{_settings.ProjectId}.appspot.com";
        }

        #region Bucket Operations

        public override async Task<bool> BucketExistsAsync(string bucketName, CancellationToken cancellationToken = default)
        {
            try
            {
                await _storageClient.GetBucketAsync(bucketName, cancellationToken: cancellationToken);
                return true;
            }
            catch (GoogleApiException ex) when (ex.HttpStatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check if bucket {BucketName} exists", bucketName);
                throw new StorageException($"Failed to check if bucket '{bucketName}' exists", ProviderType, innerException: ex);
            }
        }

        public override async Task CreateBucketAsync(string bucketName, string? region = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var bucket = new Bucket
                {
                    Name = bucketName,
                    Location = region ?? "US"
                };

                await _storageClient.CreateBucketAsync(_settings.ProjectId, bucket, cancellationToken: cancellationToken);
                _logger.LogInformation("Created bucket {BucketName}", bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create bucket {BucketName}", bucketName);
                throw new StorageException($"Failed to create bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DeleteBucketAsync(string bucketName, CancellationToken cancellationToken = default)
        {
            try
            {
                await _storageClient.DeleteBucketAsync(bucketName, cancellationToken: cancellationToken);
                _logger.LogInformation("Deleted bucket {BucketName}", bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete bucket {BucketName}", bucketName);
                throw new StorageException($"Failed to delete bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<BucketInfo>> ListBucketsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var buckets = _storageClient.ListBucketsAsync(_settings.ProjectId, cancellationToken: cancellationToken);
                var result = new List<BucketInfo>();

                await foreach (var bucket in buckets)
                {
                    result.Add(ConvertToBucketInfo(bucket));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to list buckets");
                throw new StorageException("Failed to list buckets", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region Object Operations

        public override async Task<StorageUploadResult> UploadObjectAsync(
            string bucketName, 
            string objectName, 
            Stream stream, 
            long? size = null, 
            string? contentType = null, 
            Dictionary<string, string>? metadata = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                await EnsureBucketExistsAsync(bucketName, cancellationToken);

                var obj = new Object
                {
                    Bucket = bucketName,
                    Name = objectName,
                    ContentType = contentType ?? GetContentType(objectName)
                };

                if (metadata != null)
                {
                    foreach (var kvp in metadata)
                    {
                        obj.Metadata[kvp.Key] = kvp.Value;
                    }
                }

                var uploadedObject = await _storageClient.UploadObjectAsync(obj, stream, cancellationToken: cancellationToken);

                return new StorageUploadResult
                {
                    BucketName = bucketName,
                    ObjectName = objectName,
                    Size = (long)(uploadedObject.Size ?? 0),
                    ETag = uploadedObject.ETag,
                    ContentType = uploadedObject.ContentType,
                    UploadedAt = uploadedObject.TimeCreated?.ToDateTime() ?? DateTime.UtcNow,
                    Metadata = uploadedObject.Metadata?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>(),
                    ProviderData = new Dictionary<string, object>
                    {
                        ["FirebaseGeneration"] = uploadedObject.Generation?.ToString() ?? string.Empty,
                        ["FirebaseMetageneration"] = uploadedObject.Metageneration?.ToString() ?? string.Empty
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload object {ObjectName} to bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to upload object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<StorageUploadResult> UploadFileAsync(
            string bucketName, 
            string objectName, 
            string filePath, 
            string? contentType = null, 
            Dictionary<string, string>? metadata = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"File not found: {filePath}");
                }

                using var fileStream = File.OpenRead(filePath);
                return await UploadObjectAsync(bucketName, objectName, fileStream, fileStream.Length, contentType, metadata, cancellationToken);
            }
            catch (Exception ex) when (!(ex is StorageException))
            {
                _logger.LogError(ex, "Failed to upload file {FilePath} as object {ObjectName} to bucket {BucketName}", filePath, objectName, bucketName);
                throw new StorageException($"Failed to upload file '{filePath}' as object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<StorageUploadResult> UploadLargeFileAsync(
            string bucketName, 
            string objectName, 
            string filePath, 
            string? contentType = null, 
            Dictionary<string, string>? metadata = null, 
            long partSize = 67108864, 
            IProgress<StorageUploadProgress>? progressCallback = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"File not found: {filePath}");
                }

                var fileInfo = new FileInfo(filePath);
                var fileSize = fileInfo.Length;

                // Firebase/Google Cloud Storage handles chunking internally
                // We'll use the regular upload method but with progress tracking
                using var fileStream = File.OpenRead(filePath);
                
                // Create a progress tracking stream wrapper if callback is provided
                if (progressCallback != null)
                {
                    var progressStream = new ProgressTrackingStream(fileStream, fileSize, progressCallback);
                    return await UploadObjectAsync(bucketName, objectName, progressStream, fileSize, contentType, metadata, cancellationToken);
                }
                else
                {
                    return await UploadObjectAsync(bucketName, objectName, fileStream, fileSize, contentType, metadata, cancellationToken);
                }
            }
            catch (Exception ex) when (!(ex is StorageException))
            {
                _logger.LogError(ex, "Failed to upload large file {FilePath} as object {ObjectName} to bucket {BucketName}", filePath, objectName, bucketName);
                throw new StorageException($"Failed to upload large file '{filePath}' as object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DownloadObjectAsync(string bucketName, string objectName, Stream stream, CancellationToken cancellationToken = default)
        {
            try
            {
                await _storageClient.DownloadObjectAsync(bucketName, objectName, stream, cancellationToken: cancellationToken);
            }
            catch (GoogleApiException ex) when (ex.HttpStatusCode == System.Net.HttpStatusCode.NotFound)
            {
                throw new ObjectNotFoundException(bucketName, objectName, ProviderType, ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to download object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to download object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DownloadFileAsync(string bucketName, string objectName, string filePath, CancellationToken cancellationToken = default)
        {
            try
            {
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                using var fileStream = File.Create(filePath);
                await DownloadObjectAsync(bucketName, objectName, fileStream, cancellationToken);
            }
            catch (Exception ex) when (!(ex is StorageException))
            {
                _logger.LogError(ex, "Failed to download object {ObjectName} from bucket {BucketName} to file {FilePath}", objectName, bucketName, filePath);
                throw new StorageException($"Failed to download object '{objectName}' from bucket '{bucketName}' to file '{filePath}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<byte[]> GetObjectBytesAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                using var memoryStream = new MemoryStream();
                await DownloadObjectAsync(bucketName, objectName, memoryStream, cancellationToken);
                return memoryStream.ToArray();
            }
            catch (Exception ex) when (!(ex is StorageException))
            {
                _logger.LogError(ex, "Failed to get object bytes for {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to get object bytes for '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<bool> ObjectExistsAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                await _storageClient.GetObjectAsync(bucketName, objectName, cancellationToken: cancellationToken);
                return true;
            }
            catch (GoogleApiException ex) when (ex.HttpStatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check if object {ObjectName} exists in bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to check if object '{objectName}' exists in bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DeleteObjectAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                await _storageClient.DeleteObjectAsync(bucketName, objectName, cancellationToken: cancellationToken);
                _logger.LogDebug("Deleted object {ObjectName} from bucket {BucketName}", objectName, bucketName);
            }
            catch (GoogleApiException ex) when (ex.HttpStatusCode == System.Net.HttpStatusCode.NotFound)
            {
                // Object doesn't exist, consider it already deleted
                _logger.LogDebug("Object {ObjectName} not found in bucket {BucketName}, considering it deleted", objectName, bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to delete object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<StorageDeleteResult>> DeleteObjectsAsync(string bucketName, IEnumerable<string> objectNames, CancellationToken cancellationToken = default)
        {
            var results = new List<StorageDeleteResult>();
            var objectNamesList = objectNames.ToList();

            if (!objectNamesList.Any())
            {
                return results;
            }

            // Firebase doesn't have batch delete, so we delete objects one by one
            foreach (var objectName in objectNamesList)
            {
                try
                {
                    await DeleteObjectAsync(bucketName, objectName, cancellationToken);
                    results.Add(new StorageDeleteResult
                    {
                        ObjectName = objectName,
                        Success = true
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                    results.Add(new StorageDeleteResult
                    {
                        ObjectName = objectName,
                        Success = false,
                        ErrorMessage = ex.Message,
                        ErrorCode = ex.GetType().Name
                    });
                }
            }

            return results;
        }

        public override async Task<StorageObjectInfo> GetObjectInfoAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                var obj = await _storageClient.GetObjectAsync(bucketName, objectName, cancellationToken: cancellationToken);
                return ConvertToStorageObjectInfo(obj);
            }
            catch (GoogleApiException ex) when (ex.HttpStatusCode == System.Net.HttpStatusCode.NotFound)
            {
                throw new ObjectNotFoundException(bucketName, objectName, ProviderType, ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get object info for {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to get object info for '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<StorageObjectInfo>> ListObjectsAsync(string bucketName, string? prefix = null, bool recursive = true, CancellationToken cancellationToken = default)
        {
            try
            {
                var request = new ListObjectsOptions
                {
                    Delimiter = recursive ? null : "/"
                };

                var objects = _storageClient.ListObjectsAsync(bucketName, prefix, request, cancellationToken);
                var result = new List<StorageObjectInfo>();

                await foreach (var obj in objects)
                {
                    result.Add(ConvertToStorageObjectInfo(obj));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to list objects in bucket {BucketName}", bucketName);
                throw new StorageException($"Failed to list objects in bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region URL Operations

        public override async Task<string> GetPresignedDownloadUrlAsync(string bucketName, string objectName, TimeSpan expiryTimeSpan, CancellationToken cancellationToken = default)
        {
            try
            {
                var urlSigner = UrlSigner.FromCredential(GoogleCredential.FromJson(_settings.ServiceAccountKeyJson));
                var signedUrl = await urlSigner.SignAsync(bucketName, objectName, expiryTimeSpan, HttpMethod.Get);
                return signedUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get presigned download URL for object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to get presigned download URL for object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<string> GetPresignedUploadUrlAsync(string bucketName, string objectName, TimeSpan expiryTimeSpan, CancellationToken cancellationToken = default)
        {
            try
            {
                var urlSigner = UrlSigner.FromCredential(GoogleCredential.FromJson(_settings.ServiceAccountKeyJson));
                var signedUrl = await urlSigner.SignAsync(bucketName, objectName, expiryTimeSpan, HttpMethod.Put);
                return signedUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get presigned upload URL for object {ObjectName} to bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to get presigned upload URL for object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<string> GetPreviewUrlAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                // For Firebase, we can use the public URL format or generate a long-lived signed URL
                var downloadUrl = !string.IsNullOrWhiteSpace(_settings.DownloadUrlDomain)
                    ? $"{_settings.DownloadUrlDomain}/v0/b/{bucketName}/o/{Uri.EscapeDataString(objectName)}?alt=media"
                    : await GetPresignedDownloadUrlAsync(bucketName, objectName, TimeSpan.FromHours(24), cancellationToken);

                return downloadUrl;
            }
            catch (Exception ex) when (!(ex is StorageException))
            {
                _logger.LogError(ex, "Failed to get preview URL for object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to get preview URL for object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region Helper Methods

        private BucketInfo ConvertToBucketInfo(Bucket bucket)
        {
            return new BucketInfo
            {
                Name = bucket.Name,
                CreationDate = bucket.TimeCreated?.ToDateTime() ?? DateTime.MinValue,
                Region = bucket.Location,
                Metadata = bucket.Labels?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>()
            };
        }

        private StorageObjectInfo ConvertToStorageObjectInfo(Object obj)
        {
            return new StorageObjectInfo
            {
                ObjectName = obj.Name,
                Size = (long)(obj.Size ?? 0),
                LastModified = obj.Updated?.ToDateTime() ?? DateTime.MinValue,
                ETag = obj.ETag,
                ContentType = obj.ContentType,
                StorageClass = obj.StorageClass,
                IsDirectory = obj.Name.EndsWith("/") && (obj.Size ?? 0) == 0,
                Metadata = obj.Metadata?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>(),
                ProviderData = new Dictionary<string, object>
                {
                    ["FirebaseGeneration"] = obj.Generation?.ToString() ?? string.Empty,
                    ["FirebaseMetageneration"] = obj.Metageneration?.ToString() ?? string.Empty,
                    ["FirebaseCrc32c"] = obj.Crc32c ?? string.Empty,
                    ["FirebaseMd5Hash"] = obj.Md5Hash ?? string.Empty
                }
            };
        }

        #endregion

        #region Dispose

        protected override void DisposeCore()
        {
            try
            {
                _storageClient?.Dispose();
                
                if (_firebaseApp != null)
                {
                    _firebaseApp.Delete();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error disposing Firebase storage provider");
            }
        }

        #endregion
    }

    /// <summary>
    /// Stream wrapper that tracks upload progress
    /// </summary>
    internal class ProgressTrackingStream : Stream
    {
        private readonly Stream _baseStream;
        private readonly long _totalBytes;
        private readonly IProgress<StorageUploadProgress> _progressCallback;
        private long _bytesRead;

        public ProgressTrackingStream(Stream baseStream, long totalBytes, IProgress<StorageUploadProgress> progressCallback)
        {
            _baseStream = baseStream;
            _totalBytes = totalBytes;
            _progressCallback = progressCallback;
        }

        public override bool CanRead => _baseStream.CanRead;
        public override bool CanSeek => _baseStream.CanSeek;
        public override bool CanWrite => _baseStream.CanWrite;
        public override long Length => _baseStream.Length;
        public override long Position { get => _baseStream.Position; set => _baseStream.Position = value; }

        public override void Flush() => _baseStream.Flush();

        public override int Read(byte[] buffer, int offset, int count)
        {
            var bytesRead = _baseStream.Read(buffer, offset, count);
            _bytesRead += bytesRead;
            
            _progressCallback.Report(new StorageUploadProgress
            {
                TotalBytes = _totalBytes,
                UploadedBytes = _bytesRead
            });

            return bytesRead;
        }

        public override long Seek(long offset, SeekOrigin origin) => _baseStream.Seek(offset, origin);
        public override void SetLength(long value) => _baseStream.SetLength(value);
        public override void Write(byte[] buffer, int offset, int count) => _baseStream.Write(buffer, offset, count);

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _baseStream?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
