{"ExternalStorage": {"DefaultProvider": "MinIO", "EnableFallback": false, "FallbackOrder": ["AwsS3", "Firebase"], "MinIO": {"Endpoint": "localhost:9000", "ProxyEndpoint": "https://test-proxy.example.com", "AccessKey": "test-access-key", "SecretKey": "test-secret-key", "UseSSL": false, "Region": "us-east-1", "DefaultBucket": "test-bucket", "TimeoutSeconds": 30, "CreateBucketIfNotExists": true}, "AwsS3": {"AccessKeyId": "test-aws-access-key-id", "SecretAccessKey": "test-aws-secret-access-key", "Region": "us-east-1", "DefaultBucket": "test-s3-bucket", "TimeoutSeconds": 30, "CreateBucketIfNotExists": true}, "Firebase": {"ProjectId": "test-firebase-project", "ServiceAccountKeyJson": "{\"type\":\"service_account\",\"project_id\":\"test-firebase-project\"}", "StorageBucket": "test-firebase-project.appspot.com", "DefaultBucket": "test-firebase-bucket", "TimeoutSeconds": 30, "CreateBucketIfNotExists": true}}, "Logging": {"LogLevel": {"Default": "Information", "UNI.Utilities.ExternalStorage": "Debug"}}}